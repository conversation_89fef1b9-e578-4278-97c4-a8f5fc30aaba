package oryx.mixin

import net.minecraft.client.gui.screen.ChatScreen
import org.spongepowered.asm.mixin.Mixin
import org.spongepowered.asm.mixin.injection.At
import org.spongepowered.asm.mixin.injection.Inject
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo
import oryx.event.EventManager
import oryx.event.impl.ChatEvent

@Mixin(ChatScreen::class)
class ChatScreenMixin {
    @Inject(
        method = ["sendMessage"],
        at = [At(value = "INVOKE", target = "Ljava/lang/String;startsWith(Ljava/lang/String;)Z")],
        cancellable = true
    )
    fun sendMessage(chatText: String, addToHistory: Boolean, ci: CallbackInfo) {
        val chatEvent = ChatEvent(chatText)
        EventManager.post(chatEvent)
        if (chatEvent.cancelled) ci.cancel()
    }
}